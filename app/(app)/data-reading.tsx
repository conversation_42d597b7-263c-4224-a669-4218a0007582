import React, { useEffect, useRef, useState } from "react";
import {
   ScrollView,
   Pressable,
   Alert,
   View,
   Text,
   ActivityIndicator,
   TextInput,
} from "react-native";
import { useIHealth } from "@/hooks/useIHealth";
import { useRouter, useLocalSearchParams } from "expo-router";
import { getDeviceTypeBySDKType } from "@/constants/deviceTypes";
import { useTheme } from "@/context/ThemeContext";
import { useAuth } from "@/context/AuthContext";
import useApi from "@/hooks/useApi";
// import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";

// Theme-aware color palette function
const getColors = (theme: "light" | "dark") => ({
   primary: "#4F46E5",
   primaryLight: "#818CF8",
   secondary: "#06B6D4",
   success: "#10B981",
   warning: "#F59E0B",
   error: "#EF4444",
   background: theme === "light" ? "#F8FAFC" : "#0F172A",
   cardBackground: theme === "light" ? "#FFFFFF" : "#1E293B",
   textPrimary: theme === "light" ? "#1F2937" : "#F1F5F9",
   textSecondary: theme === "light" ? "#6B7280" : "#94A3B8",
   border: theme === "light" ? "#E5E7EB" : "#334155",
   shadow: theme === "light" ? "rgba(0, 0, 0, 0.1)" : "rgba(0, 0, 0, 0.3)",
});

export default function DataReadingScreen() {
   const router = useRouter();
   const params = useLocalSearchParams();
   const { theme } = useTheme();
   const colors = getColors(theme);
   const { accessToken } = useAuth();
   const { post } = useApi();

   // Get device info from navigation params or connected device
   const deviceMac = params.deviceMac as string;
   const deviceType = params.deviceType as string;
   const deviceName = params.deviceName as string;

   // State for data submission
   const [additionalNotes, setAdditionalNotes] = useState<string>("");
   const [isSubmitting, setIsSubmitting] = useState(false);
   const [lastSubmittedReading, setLastSubmittedReading] = useState<
      string | null
   >(null);

   const {
      connectedDevice,
      connectionStatus,
      // Use generic device data methods for multi-device support
      measurementStatus,
      currentMeasurement,
      measurementHistory,
      batteryLevel,
      batteryLastUpdated,
      startMeasurement,
      stopMeasurement,
      getBatteryLevel,
      getHistoryData,
      clearMeasurementHistory,
      connectDevice,
   } = useIHealth();

   // Battery level logging removed - functionality is working correctly

   // Get device configuration
   const deviceConfig = getDeviceTypeBySDKType(deviceType);

   // Use device info from params if available, otherwise fall back to connected device
   const currentDevice = {
      mac: deviceMac || connectedDevice?.mac,
      type: deviceType || connectedDevice?.type,
      name: deviceName || connectedDevice?.name || `${deviceType} Device`,
   };

   const handleStartMeasurement = () => {
      if (!currentDevice.mac || !currentDevice.type) {
         Alert.alert(
            "No Device Information",
            "Device information is missing. Please go back and select a device.",
         );
         return;
      }

      if (connectionStatus !== "connected") {
         Alert.alert(
            "Device Not Ready",
            "Please ensure the device is connected before starting measurement.",
         );
         return;
      }

      startMeasurement(currentDevice.mac, currentDevice.type);
   };

   const handleStopMeasurement = () => {
      if (!currentDevice.type) {
         return;
      }
      stopMeasurement(currentDevice.type);
   };

   const handleGetHistory = () => {
      if (!currentDevice.mac || !currentDevice.type) {
         Alert.alert(
            "No Device Information",
            "Device information is missing. Please go back and select a device.",
         );
         return;
      }

      getHistoryData(currentDevice.mac, currentDevice.type);
   };

   // Use ref to track if battery has been fetched for current device to prevent infinite loop
   const batteryFetchedRef = useRef<string | null>(null);

   useEffect(() => {
      connectDevice({ mac: deviceMac, type: deviceType });
   }, []);

   // Auto-fetch battery data when device successfully connects (only once per device)
   useEffect(() => {
      const deviceKey = `${connectedDevice?.mac}-${connectedDevice?.type}`;

      if (
         connectionStatus === "connected" &&
         connectedDevice?.mac &&
         connectedDevice?.type &&
         batteryFetchedRef.current !== deviceKey // Only fetch if not already fetched for this device
      ) {
         // Mark as fetched immediately to prevent duplicate requests
         batteryFetchedRef.current = deviceKey;

         // Call battery level immediately
         getBatteryLevel(connectedDevice.mac, connectedDevice.type);
      } else if (connectionStatus === "disconnected") {
         Alert.alert(
            "Connection lost",
            "The connection to your device was lost. Please try again by reconnecting.",
            [{ text: "OK", onPress: () => router.replace("/scan") }],
         );
         batteryFetchedRef.current = null; // Reset when disconnected
      }
   }, [
      connectionStatus,
      connectedDevice?.mac,
      connectedDevice?.type,
      getBatteryLevel,
   ]);

   const getMeasurementStatusText = () => {
      switch (measurementStatus) {
         case "measuring":
            return "Measuring...";
         case "completed":
            return "Measurement Complete";
         case "failed":
            return "Measurement Failed";
         case "timeout":
            return "Measurement Timeout";
         default:
            return "Ready to Measure";
      }
   };

   // Data parsing function for pulse oximeter readings
   const parseDeviceDataForAPI = () => {
      if (!currentMeasurement) {
         return null;
      }

      // For pulse oximeter (PO3) data
      if (currentMeasurement.spo2 !== undefined) {
         return {
            bloodOxygen: currentMeasurement.spo2,
            pi: parseFloat((currentMeasurement.pi || 0).toFixed(2)),
            pulseRate:
               currentMeasurement.pulseRate || currentMeasurement.heartRate,
            timestamp: new Date().toISOString(),
         };
      }

      // For scale (HS2S) data - future enhancement
      if (currentMeasurement.weight !== undefined) {
         return {
            weight: currentMeasurement.weight,
            // unit: currentMeasurement.unit || "kg",
            // bmi: currentMeasurement.bmi,
            timestamp: new Date().toISOString(),
         };
      }

      return null;
   };

   // Enhanced tag metadata for medical device readings
   const generateDeviceTags = () => {
      const tags: Record<string, string> = {
         "device type": deviceConfig?.name || "Unknown Device",
      };

      if (connectedDevice) {
         tags["device model"] = connectedDevice.name || "Unknown Model";
         tags["device mac"] = connectedDevice.mac.slice(-6); // Last 6 characters for privacy
         tags["connection type"] = "Bluetooth";
      }

      if (currentMeasurement) {
         if (currentMeasurement.spo2 !== undefined) {
            tags["measurement type"] = "Pulse Oximetry";
            tags["device category"] = "Pulse Oximeter";
         } else if (currentMeasurement.weight !== undefined) {
            tags["measurement type"] = "Body Composition";
            tags["device category"] = "Smart Scale";
         }
      }

      if (batteryLevel !== null) {
         tags["battery level"] = `${batteryLevel}%`;
      }

      // Add measurement conditions
      tags["measurement environment"] = "Home";
      tags["data source"] = "iHealth SDK";
      tags["app version"] = "1.0.0"; // Could be dynamic from app.json

      return tags;
   };

   // API submission function
   const submitReadingToAPI = async () => {
      if (!currentMeasurement || !accessToken) {
         Alert.alert(
            "Cannot Submit",
            "No measurement data available or user not authenticated.",
         );
         return;
      }

      // Check if this reading was already submitted
      const readingId = `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}`;
      if (lastSubmittedReading === readingId) {
         Alert.alert(
            "Already Submitted",
            "This reading has already been submitted to the server.",
         );
         return;
      }

      const deviceReading = parseDeviceDataForAPI();
      if (!deviceReading) {
         Alert.alert(
            "Invalid Data",
            "Unable to parse device data for submission.",
         );
         return;
      }

      setIsSubmitting(true);

      try {
         const readingPayload = {
            details: {
               deviceReading,
               tags: generateDeviceTags(),
               additionalNotes:
                  additionalNotes.trim() || "No additional notes provided",
            },
         };

         console.log(readingPayload);

         await post("/readings", {
            data: readingPayload,
            token: accessToken,
         });

         // Mark this reading as submitted
         setLastSubmittedReading(readingId);

         Alert.alert(
            "Success",
            "Reading submitted successfully to the server!",
            [
               {
                  text: "OK",
                  style: "default",
               },
            ],
         );

         // Clear notes after successful submission
         setAdditionalNotes("");
      } catch (error) {
         console.error("Failed to submit reading:", error);
         Alert.alert(
            "Submission Failed",
            error instanceof Error
               ? error.message
               : "An unexpected error occurred while submitting the reading.",
         );
      } finally {
         setIsSubmitting(false);
      }
   };

   return (
      <ScrollView
         style={{ flex: 1, backgroundColor: colors.background }}
         contentContainerStyle={{ flexGrow: 1 }}
         showsVerticalScrollIndicator={false}
      >
         {/* Header */}
         <View
            style={{
               backgroundColor: colors.primary,
               paddingTop: 60,
               paddingBottom: 30,
               paddingHorizontal: 20,
               borderBottomLeftRadius: 30,
               borderBottomRightRadius: 30,
            }}
         >
            <View
               style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginBottom: 20,
               }}
            >
               <Pressable
                  onPress={() => router.back()}
                  style={{
                     backgroundColor: "rgba(255, 255, 255, 0.2)",
                     borderRadius: 12,
                     padding: 8,
                     marginRight: 15,
                  }}
               >
                  <Ionicons name="arrow-back" size={24} color="white" />
               </Pressable>
               <Text
                  style={{
                     color: "white",
                     fontSize: 24,
                     fontWeight: "bold",
                     flex: 1,
                  }}
               >
                  {deviceConfig?.name || currentDevice.name || "Device"} Monitor
               </Text>
            </View>

            {/* Device Status Card in Header */}
            <View
               style={{
                  backgroundColor: "rgba(255, 255, 255, 0.15)",
                  borderRadius: 16,
                  padding: 16,
                  flexDirection: "row",
                  alignItems: "center",
               }}
            >
               <View
                  style={{
                     backgroundColor:
                        connectionStatus === "connected"
                           ? colors.success
                           : connectionStatus === "connecting"
                             ? colors.warning
                             : connectionStatus === "failed"
                               ? colors.error
                               : colors.textSecondary,
                     borderRadius: 20,
                     width: 40,
                     height: 40,
                     justifyContent: "center",
                     alignItems: "center",
                     marginRight: 12,
                  }}
               >
                  {connectionStatus === "connecting" ? (
                     <ActivityIndicator size="small" color="white" />
                  ) : (
                     <Ionicons
                        name={
                           connectionStatus === "connected"
                              ? "checkmark"
                              : connectionStatus === "failed"
                                ? "close"
                                : "help"
                        }
                        size={20}
                        color="white"
                     />
                  )}
               </View>
               <View style={{ flex: 1 }}>
                  <Text
                     style={{
                        color: "white",
                        fontSize: 16,
                        fontWeight: "600",
                     }}
                  >
                     {connectionStatus === "connected"
                        ? "Connected"
                        : connectionStatus === "connecting"
                          ? "Connecting..."
                          : connectionStatus === "failed"
                            ? "Disconnected"
                            : "No Device Connected"}
                  </Text>
                  {connectionStatus === "connected" && connectedDevice && (
                     <Text
                        style={{
                           color: "rgba(255, 255, 255, 0.8)",
                           fontSize: 14,
                           marginTop: 2,
                        }}
                     >
                        {connectedDevice.name} • {connectedDevice.mac.slice(-6)}
                     </Text>
                  )}
               </View>
               <Text
                  style={{
                     color: "white",
                     fontSize: 24,
                  }}
               >
                  {deviceConfig?.icon || "📱"}
               </Text>
            </View>
         </View>

         <View style={{ padding: 20, flex: 1 }}>
            {/* Live Measurement Display */}
            <View
               style={{
                  backgroundColor: colors.cardBackground,
                  borderRadius: 20,
                  padding: 24,
                  marginBottom: 20,
                  shadowColor: colors.shadow,
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.1,
                  shadowRadius: 12,
                  elevation: 5,
               }}
            >
               <View
                  style={{
                     flexDirection: "row",
                     alignItems: "center",
                     marginBottom: 20,
                  }}
               >
                  <View
                     style={{
                        backgroundColor:
                           measurementStatus === "measuring"
                              ? colors.warning
                              : measurementStatus === "completed"
                                ? colors.success
                                : measurementStatus === "failed"
                                  ? colors.error
                                  : colors.textSecondary,
                        borderRadius: 12,
                        width: 24,
                        height: 24,
                        justifyContent: "center",
                        alignItems: "center",
                        marginRight: 12,
                     }}
                  >
                     {measurementStatus === "measuring" ? (
                        <ActivityIndicator size="small" color="white" />
                     ) : (
                        <Ionicons
                           name={
                              measurementStatus === "completed"
                                 ? "checkmark"
                                 : measurementStatus === "failed"
                                   ? "close"
                                   : "pulse"
                           }
                           size={14}
                           color="white"
                        />
                     )}
                  </View>
                  <Text
                     style={{
                        fontSize: 18,
                        fontWeight: "600",
                        color: colors.textPrimary,
                        flex: 1,
                     }}
                  >
                     {getMeasurementStatusText()}
                  </Text>
               </View>

               {currentMeasurement ? (
                  <View>
                     {/* Pulse Oximeter Data */}
                     {currentMeasurement.spo2 !== undefined && (
                        <View
                           style={{ flexDirection: "row", marginBottom: 16 }}
                        >
                           <View style={{ flex: 1, marginRight: 8 }}>
                              <View
                                 style={{
                                    backgroundColor: colors.primary,
                                    borderRadius: 16,
                                    padding: 16,
                                    alignItems: "center",
                                 }}
                              >
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 12,
                                       fontWeight: "500",
                                       marginBottom: 4,
                                    }}
                                 >
                                    SpO2
                                 </Text>
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 24,
                                       fontWeight: "bold",
                                    }}
                                 >
                                    {currentMeasurement.spo2}%
                                 </Text>
                              </View>
                           </View>
                           <View style={{ flex: 1, marginLeft: 8 }}>
                              <View
                                 style={{
                                    backgroundColor: colors.secondary,
                                    borderRadius: 16,
                                    padding: 16,
                                    alignItems: "center",
                                 }}
                              >
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 12,
                                       fontWeight: "500",
                                       marginBottom: 4,
                                    }}
                                 >
                                    Heart Rate
                                 </Text>
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 24,
                                       fontWeight: "bold",
                                    }}
                                 >
                                    {currentMeasurement.heartRate}
                                 </Text>
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 10,
                                       opacity: 0.8,
                                    }}
                                 >
                                    BPM
                                 </Text>
                              </View>
                           </View>
                        </View>
                     )}

                     {/* Additional metrics */}
                     {currentMeasurement.pi !== undefined && (
                        <View
                           style={{ flexDirection: "row", marginBottom: 16 }}
                        >
                           <View style={{ flex: 1, marginRight: 8 }}>
                              <View
                                 style={{
                                    backgroundColor: colors.success,
                                    borderRadius: 16,
                                    padding: 16,
                                    alignItems: "center",
                                 }}
                              >
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 12,
                                       fontWeight: "500",
                                       marginBottom: 4,
                                    }}
                                 >
                                    Pulse Rate
                                 </Text>
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 20,
                                       fontWeight: "bold",
                                    }}
                                 >
                                    {currentMeasurement.pulseRate}
                                 </Text>
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 10,
                                       opacity: 0.8,
                                    }}
                                 >
                                    BPM
                                 </Text>
                              </View>
                           </View>
                           <View style={{ flex: 1, marginLeft: 8 }}>
                              <View
                                 style={{
                                    backgroundColor: colors.warning,
                                    borderRadius: 16,
                                    padding: 16,
                                    alignItems: "center",
                                 }}
                              >
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 12,
                                       fontWeight: "500",
                                       marginBottom: 4,
                                    }}
                                 >
                                    PI
                                 </Text>
                                 <Text
                                    style={{
                                       color: "white",
                                       fontSize: 20,
                                       fontWeight: "bold",
                                    }}
                                 >
                                    {currentMeasurement.pi}
                                 </Text>
                              </View>
                           </View>
                        </View>
                     )}

                     {/* Scale Data */}
                     {currentMeasurement.weight !== undefined && (
                        <View
                           style={{
                              backgroundColor: colors.primary,
                              borderRadius: 16,
                              padding: 20,
                              alignItems: "center",
                              marginBottom: 16,
                           }}
                        >
                           <Text
                              style={{
                                 color: "white",
                                 fontSize: 14,
                                 fontWeight: "500",
                                 marginBottom: 8,
                              }}
                           >
                              Weight
                           </Text>
                           <Text
                              style={{
                                 color: "white",
                                 fontSize: 32,
                                 fontWeight: "bold",
                              }}
                           >
                              {currentMeasurement.weight}
                           </Text>
                           <Text
                              style={{
                                 color: "white",
                                 fontSize: 14,
                                 opacity: 0.8,
                              }}
                           >
                              {currentMeasurement.unit || "kg"}
                           </Text>
                           {currentMeasurement.bmi && (
                              <Text
                                 style={{
                                    color: "white",
                                    fontSize: 12,
                                    marginTop: 8,
                                    opacity: 0.9,
                                 }}
                              >
                                 BMI: {currentMeasurement.bmi}
                              </Text>
                           )}
                        </View>
                     )}

                     <Text
                        style={{
                           textAlign: "center",
                           fontSize: 12,
                           color: colors.textSecondary,
                           marginTop: 12,
                        }}
                     >
                        📅{" "}
                        {new Date(
                           currentMeasurement.timestamp,
                        ).toLocaleString()}
                     </Text>
                  </View>
               ) : (
                  <View
                     style={{
                        alignItems: "center",
                        paddingVertical: 40,
                     }}
                  >
                     <Ionicons
                        name="pulse-outline"
                        size={48}
                        color={colors.textSecondary}
                     />
                     <Text
                        style={{
                           fontSize: 16,
                           color: colors.textSecondary,
                           marginTop: 12,
                           textAlign: "center",
                        }}
                     >
                        No measurement data available
                     </Text>
                     <Text
                        style={{
                           fontSize: 14,
                           color: colors.textSecondary,
                           marginTop: 4,
                           textAlign: "center",
                        }}
                     >
                        Start a measurement to see live data
                     </Text>
                  </View>
               )}
            </View>

            {/* Data Submission Section */}
            {currentMeasurement && measurementStatus === "completed" && (
               <View
                  style={{
                     backgroundColor: colors.cardBackground,
                     borderRadius: 20,
                     padding: 24,
                     marginBottom: 20,
                     shadowColor: colors.shadow,
                     shadowOffset: { width: 0, height: 4 },
                     shadowOpacity: 0.1,
                     shadowRadius: 12,
                     elevation: 5,
                  }}
               >
                  <View
                     style={{
                        flexDirection: "row",
                        alignItems: "center",
                        marginBottom: 20,
                     }}
                  >
                     <View
                        style={{
                           backgroundColor: colors.primary,
                           borderRadius: 12,
                           width: 24,
                           height: 24,
                           justifyContent: "center",
                           alignItems: "center",
                           marginRight: 12,
                        }}
                     >
                        <Ionicons name="cloud-upload" size={14} color="white" />
                     </View>
                     <Text
                        style={{
                           fontSize: 18,
                           fontWeight: "600",
                           color: colors.textPrimary,
                           flex: 1,
                        }}
                     >
                        Submit Reading
                     </Text>
                  </View>

                  {/* Notes Input */}
                  <View style={{ marginBottom: 20 }}>
                     <Text
                        style={{
                           fontSize: 14,
                           fontWeight: "600",
                           color: colors.textPrimary,
                           marginBottom: 8,
                        }}
                     >
                        Additional Notes (Optional)
                     </Text>
                     <TextInput
                        style={{
                           backgroundColor: colors.background,
                           borderRadius: 12,
                           padding: 16,
                           fontSize: 16,
                           color: colors.textPrimary,
                           borderWidth: 1,
                           borderColor: colors.border,
                           minHeight: 80,
                           textAlignVertical: "top",
                        }}
                        placeholder="Add any notes about this reading"
                        placeholderTextColor={colors.textSecondary}
                        value={additionalNotes}
                        onChangeText={setAdditionalNotes}
                        multiline
                        numberOfLines={3}
                        maxLength={500}
                     />
                     <Text
                        style={{
                           fontSize: 12,
                           color: colors.textSecondary,
                           textAlign: "right",
                           marginTop: 4,
                        }}
                     >
                        {additionalNotes.length}/500
                     </Text>
                  </View>

                  {/* Submit Button */}
                  <Pressable
                     style={({ pressed }) => ({
                        backgroundColor:
                           lastSubmittedReading ===
                           `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}`
                              ? colors.success
                              : colors.primary,
                        borderRadius: 16,
                        padding: 18,
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "center",
                        shadowColor: colors.shadow,
                        shadowOffset: { width: 0, height: 4 },
                        shadowOpacity: 0.2,
                        shadowRadius: 8,
                        elevation: 5,
                        opacity: pressed ? 0.8 : isSubmitting ? 0.6 : 1,
                     })}
                     onPress={submitReadingToAPI}
                     disabled={isSubmitting}
                  >
                     {isSubmitting ? (
                        <ActivityIndicator
                           size="small"
                           color="white"
                           style={{ marginRight: 8 }}
                        />
                     ) : (
                        <Ionicons
                           name={
                              lastSubmittedReading ===
                              `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}`
                                 ? "checkmark-circle"
                                 : "cloud-upload"
                           }
                           size={20}
                           color="white"
                           style={{ marginRight: 8 }}
                        />
                     )}
                     <Text
                        style={{
                           color: "white",
                           fontSize: 16,
                           fontWeight: "bold",
                        }}
                     >
                        {isSubmitting
                           ? "Submitting..."
                           : lastSubmittedReading ===
                               `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}`
                             ? "Reading Submitted"
                             : "Submit to Server"}
                     </Text>
                  </Pressable>

                  {/* Submission Status */}
                  {lastSubmittedReading ===
                     `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}` && (
                     <View
                        style={{
                           flexDirection: "row",
                           alignItems: "center",
                           justifyContent: "center",
                           marginTop: 12,
                           padding: 12,
                           backgroundColor: colors.success + "20",
                           borderRadius: 12,
                        }}
                     >
                        <Ionicons
                           name="checkmark-circle"
                           size={16}
                           color={colors.success}
                        />
                        <Text
                           style={{
                              fontSize: 14,
                              color: colors.success,
                              marginLeft: 8,
                              fontWeight: "600",
                           }}
                        >
                           Successfully submitted to server
                        </Text>
                     </View>
                  )}
               </View>
            )}

            {/* Battery Status */}
            {batteryLevel !== null && (
               <View
                  style={{
                     backgroundColor: colors.cardBackground,
                     borderRadius: 16,
                     padding: 20,
                     marginBottom: 20,
                     shadowColor: colors.shadow,
                     shadowOffset: { width: 0, height: 2 },
                     shadowOpacity: 0.1,
                     shadowRadius: 8,
                     elevation: 3,
                     flexDirection: "row",
                     alignItems: "center",
                  }}
               >
                  <View
                     style={{
                        backgroundColor: colors.success,
                        borderRadius: 12,
                        width: 48,
                        height: 48,
                        justifyContent: "center",
                        alignItems: "center",
                        marginRight: 16,
                     }}
                  >
                     <Ionicons
                        name="battery-charging"
                        size={24}
                        color="white"
                     />
                  </View>
                  <View style={{ flex: 1 }}>
                     <Text
                        style={{
                           fontSize: 16,
                           fontWeight: "600",
                           color: colors.textPrimary,
                           marginBottom: 4,
                        }}
                     >
                        Battery Level
                     </Text>
                     <Text
                        style={{
                           fontSize: 24,
                           fontWeight: "bold",
                           color: colors.primary,
                        }}
                     >
                        {batteryLevel}%
                     </Text>
                     {batteryLastUpdated && (
                        <Text
                           style={{
                              fontSize: 12,
                              color: colors.textSecondary,
                              marginTop: 2,
                           }}
                        >
                           Updated:{" "}
                           {new Date(batteryLastUpdated).toLocaleTimeString()}
                        </Text>
                     )}
                  </View>
               </View>
            )}

            {/* Control Buttons */}
            <View style={{ marginBottom: 20 }}>
               <Text
                  style={{
                     fontSize: 20,
                     fontWeight: "bold",
                     color: colors.textPrimary,
                     marginBottom: 16,
                  }}
               >
                  Quick Actions
               </Text>

               {/* Primary Action Button */}
               <Pressable
                  style={({ pressed }) => ({
                     backgroundColor:
                        measurementStatus === "measuring"
                           ? colors.error
                           : colors.primary,
                     borderRadius: 16,
                     padding: 18,
                     flexDirection: "row",
                     alignItems: "center",
                     justifyContent: "center",
                     shadowColor: colors.shadow,
                     shadowOffset: { width: 0, height: 4 },
                     shadowOpacity: 0.2,
                     shadowRadius: 8,
                     elevation: 5,
                     opacity: pressed ? 0.8 : !connectedDevice ? 0.5 : 1,
                     marginBottom: 12,
                  })}
                  onPress={
                     measurementStatus === "measuring"
                        ? handleStopMeasurement
                        : handleStartMeasurement
                  }
                  disabled={!connectedDevice}
               >
                  <Ionicons
                     name={measurementStatus === "measuring" ? "stop" : "play"}
                     size={20}
                     color="white"
                     style={{ marginRight: 8 }}
                  />
                  <Text
                     style={{
                        color: "white",
                        fontSize: 16,
                        fontWeight: "bold",
                     }}
                  >
                     {measurementStatus === "measuring"
                        ? "Stop Measurement"
                        : "Start Measurement"}
                  </Text>
               </Pressable>

               {/* Secondary Action Button - Battery data is now auto-fetched */}
               <Pressable
                  style={({ pressed }) => ({
                     backgroundColor: colors.cardBackground,
                     borderRadius: 16,
                     padding: 16,
                     alignItems: "center",
                     shadowColor: colors.shadow,
                     shadowOffset: { width: 0, height: 2 },
                     shadowOpacity: 0.1,
                     shadowRadius: 6,
                     elevation: 3,
                     opacity: pressed ? 0.8 : !connectedDevice ? 0.5 : 1,
                     borderWidth: 1,
                     borderColor: colors.border,
                     flexDirection: "row",
                     justifyContent: "center",
                  })}
                  onPress={handleGetHistory}
                  disabled={!connectedDevice}
               >
                  <View
                     style={{
                        backgroundColor: colors.warning,
                        borderRadius: 12,
                        width: 40,
                        height: 40,
                        justifyContent: "center",
                        alignItems: "center",
                        marginRight: 12,
                     }}
                  >
                     <Ionicons name="time" size={20} color="white" />
                  </View>
                  <Text
                     style={{
                        fontSize: 14,
                        fontWeight: "600",
                        color: colors.textPrimary,
                     }}
                  >
                     Get History Data
                  </Text>
               </Pressable>
            </View>

            {/* Measurement History */}
            {measurementHistory.length > 0 && (
               <View
                  style={{
                     backgroundColor: colors.cardBackground,
                     borderRadius: 20,
                     padding: 20,
                     marginBottom: 20,
                     shadowColor: colors.shadow,
                     shadowOffset: { width: 0, height: 4 },
                     shadowOpacity: 0.1,
                     shadowRadius: 12,
                     elevation: 5,
                  }}
               >
                  <View
                     style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginBottom: 16,
                     }}
                  >
                     <Text
                        style={{
                           fontSize: 18,
                           fontWeight: "bold",
                           color: colors.textPrimary,
                        }}
                     >
                        Recent Measurements ({measurementHistory.length})
                     </Text>
                     <Pressable
                        style={({ pressed }) => ({
                           backgroundColor: colors.textSecondary,
                           paddingHorizontal: 12,
                           paddingVertical: 6,
                           borderRadius: 8,
                           opacity: pressed ? 0.8 : 1,
                        })}
                        onPress={clearMeasurementHistory}
                     >
                        <Text
                           style={{
                              color: "white",
                              fontSize: 12,
                              fontWeight: "600",
                           }}
                        >
                           Clear All
                        </Text>
                     </Pressable>
                  </View>

                  {measurementHistory.slice(0, 5).map((measurement, index) => (
                     <View
                        key={index}
                        style={{
                           backgroundColor: colors.background,
                           borderRadius: 12,
                           padding: 16,
                           marginBottom: 12,
                           borderLeftWidth: 4,
                           borderLeftColor: colors.primary,
                        }}
                     >
                        <View
                           style={{
                              flexDirection: "row",
                              justifyContent: "space-between",
                              marginBottom: 8,
                           }}
                        >
                           <Text
                              style={{
                                 fontSize: 16,
                                 fontWeight: "600",
                                 color: colors.textPrimary,
                              }}
                           >
                              SpO2: {measurement.spo2}%
                           </Text>
                           <Text
                              style={{
                                 fontSize: 16,
                                 fontWeight: "600",
                                 color: colors.secondary,
                              }}
                           >
                              HR: {measurement.heartRate} BPM
                           </Text>
                        </View>
                        <Text
                           style={{
                              fontSize: 12,
                              color: colors.textSecondary,
                           }}
                        >
                           📅 {new Date(measurement.timestamp).toLocaleString()}
                        </Text>
                     </View>
                  ))}
               </View>
            )}
         </View>
      </ScrollView>
   );
}
