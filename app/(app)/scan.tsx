import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ie<PERSON>, SafeAreaView, View } from "react-native";
import { useRouter } from "expo-router";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useIHealth } from "@/hooks/useIHealth";
import { IHealthDevice } from "@/hooks/useIHealthScan";
import { DeviceSelectionCard } from "@/components/ui/DeviceSelectionCard";
import { DeviceDiscoveryModal } from "@/components/ui/DeviceDiscoveryModal";
import {
   SUPPORTED_DEVICE_TYPES,
   DeviceTypeConfig,
} from "@/constants/deviceTypes";

export default function ScanScreen() {
   const router = useRouter();
   const [discoveryModalVisible, setDiscoveryModalVisible] = useState(false);
   const [selectedDeviceType, setSelectedDeviceType] =
      useState<DeviceTypeConfig | null>(null);

   const {
      isAuthenticated,
      isScanning,
      discoveredDevices,
      currentScanDeviceType,
      startDeviceTypeScan,
      stopScan,
      connectDevice,
   } = useIHealth();

   const handleDeviceTypeSelect = (deviceConfig: DeviceTypeConfig) => {
      if (!isAuthenticated) {
         Alert.alert(
            "Authentication Required",
            "Please wait for authentication to complete before scanning for devices.",
         );
         return;
      }

      setSelectedDeviceType(deviceConfig);
      setDiscoveryModalVisible(true);
      startDeviceTypeScan(deviceConfig.sdkDeviceType);
   };

   const handleDeviceSelect = (device: IHealthDevice) => {
      setDiscoveryModalVisible(false);
      stopScan();
      // connectDevice(device);

      router.push({
         pathname: "/data-reading",
         params: {
            deviceMac: device.mac,
            deviceType: device.type,
            deviceName: device.name || `${device.type} Device`,
         },
      });
   };

   const handleCloseDiscoveryModal = () => {
      setDiscoveryModalVisible(false);
      stopScan();
      setSelectedDeviceType(null);
   };

   const handleStopScan = () => {
      stopScan();
   };

   return (
      <SafeAreaView style={{ flex: 1 }}>
         <ThemedView style={{ flex: 1 }}>
            <ScrollView
               contentContainerStyle={{ flexGrow: 1 }}
               showsVerticalScrollIndicator={false}
            >
               {/* Device Type Selection Cards */}
               <View
                  style={{
                     paddingHorizontal: 20,
                     paddingTop: 20,
                     paddingBottom: 20,
                  }}
               >
                  <ThemedText
                     style={{
                        fontSize: 20,
                        fontWeight: "bold",
                        marginBottom: 16,
                        textAlign: "center",
                     }}
                  >
                     Available Device Types
                  </ThemedText>

                  {SUPPORTED_DEVICE_TYPES.map((deviceConfig) => (
                     <DeviceSelectionCard
                        key={deviceConfig.id}
                        deviceConfig={deviceConfig}
                        onPress={handleDeviceTypeSelect}
                        isScanning={
                           isScanning &&
                           currentScanDeviceType === deviceConfig.sdkDeviceType
                        }
                        disabled={!isAuthenticated}
                     />
                  ))}
               </View>
            </ScrollView>
         </ThemedView>

         {/* Device Discovery Modal */}
         <DeviceDiscoveryModal
            visible={discoveryModalVisible}
            onClose={handleCloseDiscoveryModal}
            discoveredDevices={discoveredDevices}
            isScanning={isScanning}
            scanningDeviceType={currentScanDeviceType}
            onDeviceSelect={handleDeviceSelect}
            onStopScan={handleStopScan}
         />
      </SafeAreaView>
   );
}
