import React from "react";
import { View, Text, Pressable, StyleSheet } from "react-native";
import { DeviceTypeConfig } from "@/constants/deviceTypes";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useTheme, ThemeColors } from "@/context/ThemeContext";

interface DeviceSelectionCardProps {
   deviceConfig: DeviceTypeConfig;
   onPress: (deviceConfig: DeviceTypeConfig) => void;
   isScanning?: boolean;
   disabled?: boolean;
}

export const DeviceSelectionCard: React.FC<DeviceSelectionCardProps> = ({
   deviceConfig,
   onPress,
   isScanning = false,
   disabled = false,
}) => {
   const { colors } = useTheme();
   const styles = getStyles(colors, deviceConfig.color);

   const handlePress = () => {
      if (!disabled && !isScanning) {
         onPress(deviceConfig);
      }
   };

   return (
      <Pressable
         style={({ pressed }) => [
            styles.card,
            pressed && !disabled && styles.cardPressed,
            disabled && styles.cardDisabled,
            isScanning && styles.cardScanning,
         ]}
         onPress={handlePress}
         disabled={disabled}
      >
         <ThemedView style={styles.cardContent}>
            {/* Device Icon */}
            <View style={styles.iconContainer}>
               <Text style={styles.icon}>{deviceConfig.icon}</Text>
               {isScanning && (
                  <View style={styles.scanningIndicator}>
                     <Text style={styles.scanningText}>🔍</Text>
                  </View>
               )}
            </View>

            {/* Device Info */}
            <View style={styles.infoContainer}>
               <ThemedText style={styles.deviceName}>
                  {deviceConfig.name}
               </ThemedText>
               <ThemedText style={styles.deviceDescription}>
                  {deviceConfig.description}
               </ThemedText>
               <View style={styles.categoryContainer}>
                  <Text
                     style={[
                        styles.categoryBadge,
                        { backgroundColor: deviceConfig.color },
                     ]}
                  >
                     {deviceConfig.category.replace("-", " ").toUpperCase()}
                  </Text>
               </View>
            </View>

            {/* Action Indicator */}
            <View style={styles.actionContainer}>
               {isScanning ? (
                  <View style={styles.scanningSpinner}>
                     <Text style={styles.spinnerText}>⟳</Text>
                  </View>
               ) : (
                  <Text style={styles.arrowIcon}>▶</Text>
               )}
            </View>
         </ThemedView>
      </Pressable>
   );
};

const getStyles = (colors: ThemeColors, deviceColor: string) =>
   StyleSheet.create({
      card: {
         backgroundColor: colors.inputBackground,
         borderRadius: 16,
         marginBottom: 16,
         shadowColor: "#000",
         shadowOffset: {
            width: 0,
            height: 2,
         },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 4,
         borderWidth: 1,
         borderColor: colors.border || "#e0e0e0",
      },
      cardPressed: {
         transform: [{ scale: 0.98 }],
         shadowOpacity: 0.15,
      },
      cardDisabled: {
         opacity: 0.6,
      },
      cardScanning: {
         borderColor: deviceColor,
         borderWidth: 2,
      },
      cardContent: {
         flexDirection: "row",
         alignItems: "center",
         padding: 20,
         backgroundColor: "transparent",
      },
      iconContainer: {
         position: "relative",
         marginRight: 16,
      },
      icon: {
         fontSize: 48,
         textAlign: "center",
      },
      scanningIndicator: {
         position: "absolute",
         top: -5,
         right: -5,
         backgroundColor: deviceColor,
         borderRadius: 12,
         width: 24,
         height: 24,
         justifyContent: "center",
         alignItems: "center",
      },
      scanningText: {
         fontSize: 12,
         color: "white",
      },
      infoContainer: {
         flex: 1,
      },
      deviceName: {
         fontSize: 20,
         fontWeight: "bold",
         marginBottom: 4,
      },
      deviceDescription: {
         fontSize: 14,
         color: colors.icon,
         marginBottom: 8,
         lineHeight: 20,
      },
      categoryContainer: {
         flexDirection: "row",
      },
      categoryBadge: {
         paddingHorizontal: 8,
         paddingVertical: 4,
         borderRadius: 12,
         fontSize: 10,
         fontWeight: "bold",
         color: "white",
         textAlign: "center",
         overflow: "hidden",
      },
      actionContainer: {
         justifyContent: "center",
         alignItems: "center",
         width: 40,
      },
      arrowIcon: {
         fontSize: 20,
         color: colors.icon,
      },
      scanningSpinner: {
         width: 30,
         height: 30,
         justifyContent: "center",
         alignItems: "center",
      },
      spinnerText: {
         fontSize: 24,
         color: deviceColor,
         // Add animation here if needed
      },
   });
