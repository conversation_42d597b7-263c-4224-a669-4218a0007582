import { useEffect, useRef } from "react";
import { useIHealthPermissions } from "./useIHealthPermissions";
import { useIHealthAuth } from "./useIHealthAuth";
import { useIHealthScan } from "./useIHealthScan";
import { useIHealthConnect } from "./useIHealthConnect";

import { useIHealthDeviceData } from "./useIHealthDeviceData";
import { Alert } from "react-native";

export const useIHealth = () => {
   const { permissionStatus, requestPermissions } = useIHealthPermissions();
   const { isAuthenticated, authenticate } = useIHealthAuth();
   const scan = useIHealthScan();
   const connection = useIHealthConnect();
   const deviceData = useIHealthDeviceData();
   const initCalled = useRef(false);

   useEffect(() => {
      if (initCalled.current) {
         return;
      }
      initCalled.current = true;

      const init = async () => {
         const permissionsGranted = await requestPermissions();
         if (permissionsGranted) {
            authenticate();
         } else {
            Alert.alert(
               "Permissions Denied",
               "AdaptIT will not be able to connect to devices without the appropriate permissions. You will still have acess to other features of the app.",
            );
            console.warn("Permissions denied. iHealth SDK cannot function.");
         }
      };
      init();
   }, [requestPermissions, authenticate]);

   return {
      permissionStatus,
      isAuthenticated,
      ...scan,
      ...connection,
      // Use generic device data methods for all devices
      measurementStatus: deviceData.measurementStatus,
      currentMeasurement: deviceData.currentMeasurement,
      measurementHistory: deviceData.measurementHistory,
      batteryLevel: deviceData.batteryLevel,
      batteryLastUpdated: deviceData.batteryLastUpdated,
      startMeasurement: deviceData.startMeasurement,
      stopMeasurement: deviceData.stopMeasurement,
      getBatteryLevel: deviceData.getBatteryLevel,
      getHistoryData: deviceData.getHistoryData,
      clearMeasurementHistory: deviceData.clearMeasurementHistory,
   };
};
