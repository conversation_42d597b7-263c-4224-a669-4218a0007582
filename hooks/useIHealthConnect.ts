import { useState, useEffect, useRef } from "react";
import { DeviceEventEmitter } from "react-native";
import { iHealthDeviceManagerModule } from "@ihealth/ihealthlibrary-react-native";
import { IHealthDevice } from "./useIHealthScan";

export type DeviceConnectionStatus =
   | "disconnected"
   | "connecting"
   | "connected"
   | "waiting"
   | "failed";

export interface UseIHealthConnectReturn {
   connectionStatus: DeviceConnectionStatus;
   connectedDevice: IHealthDevice | null;
   connectDevice: (device: IHealthDevice) => void;
   disconnectDevice: (device: IHealthDevice) => void;
   connectPO3Device: (device: IHealthDevice) => void;
}

export const useIHealthConnect = (): UseIHealthConnectReturn => {
   const [connectionStatus, setConnectionStatus] =
      useState<DeviceConnectionStatus>("waiting");
   const [connectedDevice, setConnectedDevice] = useState<IHealthDevice | null>(
      null,
   );
   const connectionTimeoutRef = useRef<number | null>(null);
   const connectingDeviceRef = useRef<IHealthDevice | null>(null);

   useEffect(() => {
      const clearConnectionTimeout = () => {
         if (connectionTimeoutRef.current) {
            clearTimeout(connectionTimeoutRef.current);
            connectionTimeoutRef.current = null;
         }
      };

      const handleConnection = (
         event: IHealthDevice,
         status: DeviceConnectionStatus,
      ) => {
         clearConnectionTimeout();

         setConnectionStatus(status);
         if (status === "connected") {
            setConnectedDevice(event);
         } else {
            setConnectedDevice(null);
            if (status === "failed") {
               console.warn(`Connection failed for device ${event.mac}`);
            }
         }

         connectingDeviceRef.current = null;
      };

      const connectedListener = DeviceEventEmitter.addListener(
         iHealthDeviceManagerModule.Event_Device_Connected,
         (event: IHealthDevice) => {
            handleConnection(event, "connected");
         },
      );

      const connectFailListener = DeviceEventEmitter.addListener(
         iHealthDeviceManagerModule.Event_Device_Connect_Failed,
         (event: IHealthDevice) => {
            handleConnection(event, "failed");
         },
      );

      const disconnectListener = DeviceEventEmitter.addListener(
         iHealthDeviceManagerModule.Event_Device_Disconnect,
         (event: IHealthDevice) => {
            handleConnection(event, "disconnected");
         },
      );

      return () => {
         connectedListener.remove();
         connectFailListener.remove();
         disconnectListener.remove();
      };
   }, []);

   const connectDevice = (device: IHealthDevice) => {
      if (connectionTimeoutRef.current) {
         clearTimeout(connectionTimeoutRef.current);
         connectionTimeoutRef.current = null;
      }

      setConnectionStatus("connecting");
      connectingDeviceRef.current = device;

      try {
         const timeout = setTimeout(() => {
            console.log(`Connection timeout for device ${device.mac}`);
            setConnectionStatus("failed");
            connectingDeviceRef.current = null;
         }, 20000);
         connectionTimeoutRef.current = timeout;
         console.log(`Setting timeout ref: ${connectionTimeoutRef.current}`);

         // Always call this last to prevent race conditions.
         iHealthDeviceManagerModule.connectDevice(device.mac, device.type);
      } catch (error) {
         console.error(`Failed to initiate connection:`, error);
         setConnectionStatus("failed");
         connectingDeviceRef.current = null;
      }
   };

   const disconnectDevice = (device: IHealthDevice) => {
      if (device) {
         try {
            iHealthDeviceManagerModule.disconnectDevice(
               device.mac,
               device.type,
            );
         } catch (error) {
            console.error(`Failed to disconnect:`, error);
         }
      }
   };

   const connectPO3Device = (device: IHealthDevice) => {
      if (device.type !== "PO3") {
         console.error(`Device is not a PO3 pulse oximeter`);
         return;
      }

      connectDevice(device);
   };

   return {
      connectionStatus,
      connectedDevice,
      connectDevice,
      disconnectDevice,
      connectPO3Device,
   };
};
